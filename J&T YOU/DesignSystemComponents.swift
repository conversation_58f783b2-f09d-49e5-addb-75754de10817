//
//  DesignSystemComponents.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI
import UIKit

// MARK: - Design System Components

/// Reusable UI components following the J&T YOU design system
struct DesignSystemComponents {

    // MARK: - Background Components

    /// Main app background with gradient
    struct AppBackground: View {
        var body: some View {
            Rectangle()
                .foregroundColor(.clear)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(
                    LinearGradient(
                        gradient: Gradient(stops: [
                            .init(color: DesignSystem.Colors.backgroundGradientTop, location: 0.0),
                            .init(color: DesignSystem.Colors.backgroundGradientBottom, location: 1.0)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        }
    }

    // MARK: - Glassmorphic Components

    /// Glassmorphic container with thinMaterial effect
    struct GlassmorphicContainer<Content: View>: View {
        let content: Content
        let cornerRadius: CGFloat
        let borderOpacity: Double
        let backgroundOpacity: Double

        init(
            cornerRadius: CGFloat = DesignSystem.CornerRadius.md,
            borderOpacity: Double = 0.25,
            backgroundOpacity: Double = 1.0,
            @ViewBuilder content: () -> Content
        ) {
            self.content = content()
            self.cornerRadius = cornerRadius
            self.borderOpacity = borderOpacity
            self.backgroundOpacity = backgroundOpacity
        }

        var body: some View {
            content
                .background(
                    ZStack {
                        // Base color layer
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(DesignSystem.Colors.surfacePrimary) // #01221F
                            .opacity(backgroundOpacity)

                        // Glassmorphic blur overlay (optional, can be removed if solid color is preferred)
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .fill(DesignSystem.BlurEffects.thin)
                            .opacity(0.0) // Set to 0 to disable blur, increase for glassmorphic effect
                    }
                    .overlay(
                        RoundedRectangle(cornerRadius: cornerRadius)
                            .stroke(DesignSystem.Colors.borderPrimary.opacity(borderOpacity), lineWidth: 0.5)
                    )
                )
        }
    }

    /// Glassmorphic navigation bar with smooth blur effect
    struct GlassmorphicNavigationBar<Content: View>: View {
        let content: Content
        let isVisible: Bool
        let opacity: Double
        let blurIntensity: Double
        let backgroundOpacity: Double

        init(
            isVisible: Bool = true,
            opacity: Double = 0.9,
            blurIntensity: Double = 0.5,
            backgroundOpacity: Double = 0.1,
            @ViewBuilder content: () -> Content
        ) {
            self.content = content()
            self.isVisible = isVisible
            self.opacity = opacity
            self.blurIntensity = max(0.0, min(1.0, blurIntensity)) // Clamp between 0 and 1
            self.backgroundOpacity = max(0.0, min(1.0, backgroundOpacity)) // Clamp between 0 and 1
        }

        var body: some View {
            content
                .background(
                    Group {
                        if isVisible {
                            ZStack {
                                // Enhanced background color overlay for better contrast
                                Rectangle()
                                    .fill(
                                        Color.lerp(
                                            from: DesignSystem.Colors.navigationBackground,
                                            to: backgroundOpacity > 0.7 ?
                                                DesignSystem.Colors.navigationBackgroundHigh :
                                                DesignSystem.Colors.navigationBackgroundScrolled,
                                            amount: backgroundOpacity
                                        )
                                    )
                                    .opacity(min(backgroundOpacity * 1.2, 1.0)) // Boost background opacity

                                // Dynamic blur effect with enhanced intensity
                                Rectangle()
                                    .fill(DesignSystem.BlurEffects.navigationBlur(intensity: blurIntensity))
                                    .opacity(min(opacity * 1.1, 1.0)) // Boost blur opacity
                            }
                        } else {
                            Color.clear
                        }
                    }
                )
                .animation(DesignSystem.Animation.navigationBlur, value: blurIntensity)
                .animation(DesignSystem.Animation.navigationFade, value: opacity)
                .animation(DesignSystem.Animation.navigationBackground, value: backgroundOpacity)
        }
    }

    // MARK: - Visual Effect View (UIKit Bridge)

    /// UIKit Visual Effect View wrapper for enhanced blur effects
    struct VisualEffectView: UIViewRepresentable {
        let effect: UIVisualEffect
        let intensity: Double

        init(effect: UIVisualEffect, intensity: Double = 1.0) {
            self.effect = effect
            self.intensity = max(0.0, min(1.0, intensity))
        }

        func makeUIView(context: Context) -> UIVisualEffectView {
            let view = UIVisualEffectView(effect: effect)
            view.alpha = intensity
            return view
        }

        func updateUIView(_ uiView: UIVisualEffectView, context: Context) {
            uiView.effect = effect
            uiView.alpha = intensity
        }
    }

    // MARK: - Top Navigation Component

    /// Native-style TopNavigation with iOS App Library blur effect
    struct TopNavigation<Leading: View, Trailing: View>: View {
        let title: String?
        let isVisible: Bool
        let scrollOffset: CGFloat
        let leading: Leading
        let trailing: Trailing

        init(
            title: String? = nil,
            isVisible: Bool = true,
            scrollOffset: CGFloat = 0,
            @ViewBuilder leading: () -> Leading = { EmptyView() },
            @ViewBuilder trailing: () -> Trailing = { EmptyView() }
        ) {
            self.title = title
            self.isVisible = isVisible
            self.scrollOffset = scrollOffset
            self.leading = leading()
            self.trailing = trailing()
        }

        var body: some View {
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // Navigation content
                    HStack {
                        // Leading content
                        HStack {
                            leading
                        }
                        .frame(minWidth: 44, alignment: .leading)

                        Spacer()

                        // Title (if provided)
                        if let title = title {
                            Text(title)
                                .font(DesignSystem.Typography.headlineFont)
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .opacity(titleOpacity)
                                .animation(DesignSystem.Animation.navigationFade, value: titleOpacity)
                        }

                        Spacer()

                        // Trailing content
                        HStack {
                            trailing
                        }
                        .frame(minWidth: 44, alignment: .trailing)
                    }
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                    .padding(.top, geometry.safeAreaInsets.top > 0 ? DesignSystem.Spacing.sm : DesignSystem.Spacing.lg)
                    .padding(.bottom, DesignSystem.Spacing.md)
                    .opacity(isVisible ? 1.0 : 0.0)

                    Spacer()
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(
                    Group {
                        if isVisible && blurIntensity > 0.1 {
                            ZStack {
                                // Enhanced background with App Library style
                                Rectangle()
                                    .fill(
                                        Color.lerp(
                                            from: Color.clear,
                                            to: DesignSystem.Colors.navigationBackgroundHigh,
                                            amount: backgroundOpacity
                                        )
                                    )
                                    .opacity(backgroundOpacity)

                                // App Library style blur with enhanced saturation
                                VisualEffectView(
                                    effect: UIBlurEffect(style: blurStyle),
                                    intensity: blurIntensity
                                )

                                // Subtle gradient overlay for depth
                                LinearGradient(
                                    gradient: Gradient(stops: [
                                        .init(color: Color.white.opacity(0.1), location: 0.0),
                                        .init(color: Color.clear, location: 0.3),
                                        .init(color: Color.black.opacity(0.05), location: 1.0)
                                    ]),
                                    startPoint: .top,
                                    endPoint: .bottom
                                )
                                .opacity(blurIntensity * 0.6)
                            }
                        } else {
                            Color.clear
                        }
                    }
                )
                .overlay(
                    // Soft shadow at bottom for separation
                    Rectangle()
                        .fill(
                            LinearGradient(
                                gradient: Gradient(stops: [
                                    .init(color: Color.clear, location: 0.0),
                                    .init(color: Color.black.opacity(0.1), location: 1.0)
                                ]),
                                startPoint: .top,
                                endPoint: .bottom
                            )
                        )
                        .frame(height: 1)
                        .opacity(blurIntensity * 0.8),
                    alignment: .bottom
                )
                .animation(DesignSystem.Animation.navigationBlur, value: blurIntensity)
                .animation(DesignSystem.Animation.navigationFade, value: backgroundOpacity)
            }
            .frame(height: navigationHeight)
            .clipped()
        }

        // MARK: - Computed Properties

        private var navigationHeight: CGFloat {
            // Dynamic height based on safe area
            let baseHeight: CGFloat = 44 + DesignSystem.Spacing.lg + DesignSystem.Spacing.md
            let safeAreaTop = UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first?.windows.first?.safeAreaInsets.top ?? 0
            return baseHeight + (safeAreaTop > 0 ? safeAreaTop + DesignSystem.Spacing.sm : DesignSystem.Spacing.lg)
        }

        private var blurIntensity: Double {
            let maxBlurOffset: CGFloat = 60
            let normalizedOffset = max(0, min(scrollOffset, maxBlurOffset))
            let intensity = Double(normalizedOffset / maxBlurOffset)

            // Enhanced curve for more dramatic App Library effect
            return pow(intensity, 0.7) // Slightly faster blur onset
        }

        private var backgroundOpacity: Double {
            let maxBackgroundOffset: CGFloat = 80
            let normalizedOffset = max(0, min(scrollOffset, maxBackgroundOffset))
            let baseOpacity = Double(normalizedOffset / maxBackgroundOffset)

            // Progressive opacity with enhanced contrast
            return min(baseOpacity * 0.9 + (baseOpacity > 0.6 ? 0.1 : 0), 1.0)
        }

        private var titleOpacity: Double {
            let titleThreshold: CGFloat = 30
            return scrollOffset > titleThreshold ? 1.0 : 0.0
        }

        private var blurStyle: UIBlurEffect.Style {
            // App Library uses a rich, saturated blur
            if blurIntensity < 0.3 {
                return .systemUltraThinMaterialDark
            } else if blurIntensity < 0.7 {
                return .systemThinMaterialDark
            } else {
                return .systemMaterialDark
            }
        }
    }

    // MARK: - Button Components

    /// Circular icon button with border
    struct CircularIconButton: View {
        let icon: String
        let isFilled: Bool
        let action: () -> Void

        init(icon: String, isFilled: Bool = false, action: @escaping () -> Void) {
            self.icon = icon
            self.isFilled = isFilled
            self.action = action
        }

        var body: some View {
            Button(action: action) {
                ZStack {
                    Circle()
                        .fill(isFilled ? DesignSystem.Colors.textPrimary : Color.clear)
                        .stroke(isFilled ? Color.clear : DesignSystem.Colors.borderSecondary, lineWidth: 0.5)
                        .frame(width: DesignSystem.Dimensions.iconButtonSize, height: DesignSystem.Dimensions.iconButtonSize)

                    if icon.count == 1 {
                        // Text icon (like "?")
                        Text(icon)
                            .font(DesignSystem.Typography.bodyFont)
                            .foregroundColor(isFilled ? DesignSystem.Colors.textInverse : DesignSystem.Colors.textSecondary)
                    } else {
                        // System icon
                        Image(systemName: icon)
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(isFilled ? DesignSystem.Colors.textInverse : DesignSystem.Colors.textPrimary)
                    }
                }
            }
        }
    }

    /// Quick action button with glassmorphic style
    struct QuickActionButton: View {
        let title: String
        let action: () -> Void

        init(title: String, action: @escaping () -> Void) {
            self.title = title
            self.action = action
        }

        var body: some View {
            Button(action: action) {
                Text(title)
                    .font(DesignSystem.Typography.bodyFont)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                    .padding(.horizontal, DesignSystem.Spacing.xxl)
                    .padding(.vertical, DesignSystem.Spacing.md)
                    .background(
                        RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.button)
                            .stroke(DesignSystem.Colors.borderPrimary, lineWidth: 0.5)
                    )
            }
        }
    }

    // MARK: - Input Components

    /// Glassmorphic text input field
    struct GlassmorphicTextField: View {
        @Binding var text: String
        let placeholder: String
        let onSubmit: () -> Void

        init(text: Binding<String>, placeholder: String, onSubmit: @escaping () -> Void = {}) {
            self._text = text
            self.placeholder = placeholder
            self.onSubmit = onSubmit
        }

        var body: some View {
            TextField(placeholder, text: $text, axis: .vertical)
                .font(DesignSystem.Typography.bodyFont)
                .foregroundColor(DesignSystem.Colors.textPrimary)
                .lineLimit(1...5)
                .onSubmit(onSubmit)
        }
    }

    // MARK: - Chat Components

    /// Chat message bubble
    struct MessageBubble: View {
        let message: String
        let isFromUser: Bool

        init(message: String, isFromUser: Bool) {
            self.message = message
            self.isFromUser = isFromUser
        }

        var body: some View {
            HStack {
                if isFromUser {
                    Spacer()

                    Text(message)
                        .font(DesignSystem.Typography.bodyFont)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.messageBubble)
                                .fill(DesignSystem.Colors.primaryTeal)
                        )
                        .frame(maxWidth: DesignSystem.Dimensions.messageBubbleMaxWidth, alignment: .trailing)
                } else {
                    Text(message)
                        .font(DesignSystem.Typography.bodyFont)
                        .foregroundColor(DesignSystem.Colors.textPrimary)
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        .padding(.vertical, DesignSystem.Spacing.md)
                        .background(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.messageBubble)
                                .fill(DesignSystem.Colors.surfacePrimary)
                        )
                        .frame(maxWidth: DesignSystem.Dimensions.messageBubbleMaxWidth, alignment: .leading)

                    Spacer()
                }
            }
        }
    }

    // MARK: - Orb Components

    /// Central animated orb
    struct AnimatedOrb: View {
        @Binding var isAnimating: Bool
        let onTap: () -> Void

        init(isAnimating: Binding<Bool>, onTap: @escaping () -> Void) {
            self._isAnimating = isAnimating
            self.onTap = onTap
        }

        var body: some View {
            ZStack {
                // Glow effect
                Circle()
                    .fill(DesignSystem.Colors.primaryTealDark.opacity(0.80))
                    .frame(width: DesignSystem.Dimensions.orbGlowSize, height: DesignSystem.Dimensions.orbGlowSize)
                    .blur(radius: 50)
                    .scaleEffect(isAnimating ? 1.2 : 1.0)
                    .animation(DesignSystem.Animation.orbPulse, value: isAnimating)

                // Main orb
                Circle()
                    .fill(
                        RadialGradient(
                            gradient: Gradient(colors: [
                                DesignSystem.Colors.primaryTealLight,
                                DesignSystem.Colors.primaryTealDark
                            ]),
                            center: .center,
                            startRadius: 20,
                            endRadius: 100
                        )
                    )
                    .frame(width: DesignSystem.Dimensions.orbSize, height: DesignSystem.Dimensions.orbSize)
                    .overlay(
                        Circle()
                            .stroke(DesignSystem.Colors.shadowSecondary, lineWidth: 1)
                    )
                    .designSystemShadow(DesignSystem.Shadows.orb)
                    .scaleEffect(isAnimating ? 1.05 : 1.0)
                    .animation(DesignSystem.Animation.orbScale, value: isAnimating)
                    .onTapGesture(perform: onTap)
            }
        }
    }

    // MARK: - Home Indicator

    /// iPhone-style home indicator with glassmorphic background
    /// Note: This is a custom visual element, not a replacement for the system home indicator
    struct HomeIndicator: View {
        var body: some View {
            GeometryReader { geometry in
                Rectangle()
                    .fill(DesignSystem.BlurEffects.thin)
                    .frame(maxWidth: .infinity)
                    .overlay(
                        Rectangle()
                            .fill(DesignSystem.Colors.textPrimary)
                            .frame(width: DesignSystem.Dimensions.homeIndicatorWidth, height: DesignSystem.Dimensions.homeIndicatorThickness)
                            .cornerRadius(DesignSystem.Dimensions.homeIndicatorThickness / 2)
                            .offset(y: -DesignSystem.Spacing.sm)
                    )
                    .clipShape(
                        UnevenRoundedRectangle(
                            topLeadingRadius: 0,
                            bottomLeadingRadius: DesignSystem.CornerRadius.iPhoneBezel,
                            bottomTrailingRadius: DesignSystem.CornerRadius.iPhoneBezel,
                            topTrailingRadius: 0
                        )
                    )
            }
        }
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: DesignSystem.Spacing.lg) {
        DesignSystemComponents.CircularIconButton(icon: "?") {
            print("Button tapped")
        }

        DesignSystemComponents.QuickActionButton(title: "Test Button") {
            print("Quick action tapped")
        }

        DesignSystemComponents.MessageBubble(message: "Hello, this is a test message!", isFromUser: true)

        DesignSystemComponents.MessageBubble(message: "This is a response message.", isFromUser: false)
    }
    .padding()
    .background(DesignSystem.Colors.backgroundPrimary)
}
