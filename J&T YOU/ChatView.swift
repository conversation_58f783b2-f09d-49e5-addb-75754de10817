//
//  ChatView.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 28.05.2025.
//

import SwiftUI
import PhotosUI
import UniformTypeIdentifiers
import AVFoundation

// MARK: - Chat View

struct ChatView: View {
    @State private var messageText: String = ""
    @State private var messages: [ChatMessage] = []
    @State private var isOrbAnimating: Bool = false
    @State private var showQuickActions: Bool = false
    @State private var showChatMessages: Bool = false
    @State private var isTyping: Bool = false
    @State private var scrollOffset: CGFloat = 0

    // File picker states
    @State private var showingActionSheet = false
    @State private var selectedPhotoItem: PhotosPickerItem?
    @State private var showingImagePicker = false
    @State private var showingDocumentPicker = false
    @State private var isProcessingFile = false
    @State private var cameraSourceType: CameraSourceType = .camera
    @State private var pendingUploads: [PendingUpload] = []

    enum CameraSourceType {
        case camera
        case photoLibrary
    }

    struct PendingUpload {
        let data: Data
        let fileName: String
        let mimeType: String
    }

    // Store current upload data for receipt creation
    @State private var currentUploadData: Data?

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                backgroundLayer

                // Main content
                VStack(spacing: 0) {
                    // Main content area
                    ZStack {
                        // Central orb (only show when not in chat mode)
                        if !showChatMessages {
                            centralOrbLayer
                        }

                        // Chat messages (only show when in chat mode)
                        if showChatMessages && !messages.isEmpty {
                            chatMessagesLayer
                        }

                        // Quick action buttons (only show when not in chat mode)
                        if !showChatMessages {
                            quickActionsLayer
                        }
                    }

                    Spacer()

                    // Bottom input area - always visible
                    bottomInputLayer
                }

                // Top navigation overlay with glassmorphic effect
                topNavigationOverlay
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(DesignSystem.Colors.backgroundPrimary)
        .ignoresSafeArea(.container, edges: .horizontal)
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    dismissKeyboard()
                }
        )
        .onAppear {
            startOrbAnimation()
            showQuickActionsWithDelay()
        }
        .confirmationDialog("Select File", isPresented: $showingActionSheet) {
            Button("Take Photo") {
                requestCameraPermissionAndOpen()
            }

            PhotosPicker(selection: $selectedPhotoItem, matching: .images) {
                Text("Choose from Gallery")
            }

            Button("Choose Document") {
                showingDocumentPicker = true
            }

            Button("Cancel", role: .cancel) { }
        }
        .onChange(of: selectedPhotoItem) { _, newItem in
            if let newItem = newItem {
                processPhotoPickerItem(newItem)
            }
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePickerView(
                isPresented: $showingImagePicker,
                sourceType: cameraSourceType == .camera ? .camera : .photoLibrary
            ) { image in
                processImage(image)
            }
        }
        .sheet(isPresented: $showingDocumentPicker) {
            DocumentPickerView(isPresented: $showingDocumentPicker) { url in
                processDocument(url)
            }
        }
    }
}

// MARK: - View Layers

extension ChatView {

    private var backgroundLayer: some View {
        DesignSystemComponents.AppBackground()
    }

    private var topNavigationOverlay: some View {
        VStack(spacing: 0) {
            DesignSystemComponents.GlassmorphicNavigationBar(
                isVisible: showChatMessages && scrollOffset > 10, // Lower threshold for easier testing
                opacity: calculateNavigationOpacity(),
                blurIntensity: calculateBlurIntensity(),
                backgroundOpacity: calculateBackgroundOpacity()
            ) {
                HStack {
                    // Hamburger menu button (top left)
                    DesignSystemComponents.CircularIconButton(icon: "line.3.horizontal") {
                        resetToInitialState()
                    }

                    Spacer()

                    // New chat button
                    DesignSystemComponents.CircularIconButton(icon: "square.and.pencil") {
                        // Handle new chat action
                        resetToInitialState()
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.xl) // Increased horizontal padding
                .padding(.vertical, DesignSystem.Spacing.xl)   // Increased vertical padding for better presence
            }

            Spacer()
        }
    }

    private var centralOrbLayer: some View {
        VStack {
            Spacer()

            DesignSystemComponents.AnimatedOrb(isAnimating: $isOrbAnimating) {
                startConversation()
            }

            Spacer()
        }
    }

    private var chatMessagesLayer: some View {
        ScrollViewReader { proxy in
            ScrollView {
                GeometryReader { geometry in
                    Color.clear.preference(key: ScrollOffsetPreferenceKey.self, value: geometry.frame(in: .named("scroll")).minY)
                }
                .frame(height: 0)

                LazyVStack(spacing: DesignSystem.Spacing.lg) {
                    ForEach(messages, id: \.id) { message in
                        if let receiptData = message.receiptData {
                            // Show receipt card for OCR results
                            HStack {
                                ReceiptCardView(receiptData: receiptData)
                                Spacer()
                            }
                            .id(message.id)
                        } else if let investmentData = message.investmentData {
                            // Show investment plan widget
                            HStack {
                                InvestmentPlanWidget(investmentData: investmentData)
                                Spacer()
                            }
                            .id(message.id)
                        } else {
                            // Show regular message bubble
                            DesignSystemComponents.MessageBubble(
                                message: message.content,
                                isFromUser: message.isFromUser
                            )
                            .id(message.id)
                        }
                    }

                    if isTyping {
                        TypingIndicator()
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .safeAreaInset(edge: .top) {
                    Color.clear.frame(height: DesignSystem.Spacing.xxxl)
                }
                .safeAreaInset(edge: .bottom) {
                    Color.clear.frame(height: 120) // Space for input area
                }
            }
            .coordinateSpace(name: "scroll")
            .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                scrollOffset = -value
            }
            .onChange(of: messages.count) { _, _ in
                if let lastMessage = messages.last {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        proxy.scrollTo(lastMessage.id, anchor: .bottom)
                    }
                }
            }
        }
    }

    private var quickActionsLayer: some View {
        VStack {
            Spacer()

            // Upload receipt button - positioned 20pt above chat interface
            DesignSystemComponents.QuickActionButton(title: "Nahrát účtenku") {
                handleQuickAction("Nahrát účtenku")
            }
            .opacity(showQuickActions ? 1.0 : 0.0)
            .offset(y: showQuickActions ? 0 : 20)
            .animation(DesignSystem.Animation.springMedium.delay(0.3), value: showQuickActions)
            .padding(.bottom, 20) // 20pt above chat interface
        }
    }

    private var bottomInputLayer: some View {
        VStack(spacing: 0) {
            // Input area with glassmorphic background
            DesignSystemComponents.GlassmorphicContainer(
                cornerRadius: DesignSystem.CornerRadius.md
            ) {
                HStack(spacing: DesignSystem.Spacing.lg) {
                    // Left button (plus circle for file upload)
                    DesignSystemComponents.CircularIconButton(icon: "plus.circle") {
                        showingActionSheet = true
                    }

                    // Text input
                    DesignSystemComponents.GlassmorphicTextField(
                        text: $messageText,
                        placeholder: "Napište..."
                    ) {
                        sendMessage()
                    }

                    // Send button
                    DesignSystemComponents.CircularIconButton(icon: "paperplane.fill", isFilled: true) {
                        if messageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            handleVoiceInput()
                        } else {
                            sendMessage()
                        }
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.lg)
                .padding(.vertical, DesignSystem.Spacing.lg)
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.top, DesignSystem.Spacing.sm)
            .safeAreaInset(edge: .bottom) {
                Color.clear.frame(height: DesignSystem.Spacing.sm)
            }
        }
    }


}

// MARK: - Helper Methods

extension ChatView {

    // MARK: - Navigation Effect Calculations

    /// Calculates the navigation bar opacity based on scroll offset
    private func calculateNavigationOpacity() -> Double {
        let maxOpacity = 1.0 // Full opacity for maximum effect
        let scrollThreshold: CGFloat = 60 // Even faster opacity increase
        return min(scrollOffset / scrollThreshold, maxOpacity)
    }

    /// Calculates the blur intensity based on scroll offset (0.0 to 1.0)
    /// Enhanced for more dramatic visual impact
    private func calculateBlurIntensity() -> Double {
        let maxBlurOffset: CGFloat = 80 // Faster blur increase
        let normalizedOffset = max(0, min(scrollOffset, maxBlurOffset))
        let intensity = Double(normalizedOffset / maxBlurOffset)

        // Apply easing curve for more dramatic effect
        return pow(intensity, 0.8) // Slightly accelerated curve
    }

    /// Calculates the background opacity for better contrast
    /// Enhanced for maximum visual separation
    private func calculateBackgroundOpacity() -> Double {
        let maxBackgroundOffset: CGFloat = 70 // Faster background increase
        let normalizedOffset = max(0, min(scrollOffset, maxBackgroundOffset))
        let baseOpacity = Double(normalizedOffset / maxBackgroundOffset)

        // Progressive opacity with boost at higher scroll values
        return min(baseOpacity * 0.8 + (baseOpacity > 0.5 ? 0.2 : 0), 1.0)
    }

    private func startOrbAnimation() {
        withAnimation(DesignSystem.Animation.easeInOutFast.delay(0.5)) {
            isOrbAnimating = true
        }
    }

    private func showQuickActionsWithDelay() {
        withAnimation(DesignSystem.Animation.springMedium.delay(1.0)) {
            showQuickActions = true
        }
    }

    private func startConversation() {
        withAnimation(DesignSystem.Animation.springMedium) {
            showChatMessages = true
        }

        // Add welcome message
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            addMessage("Ahoj! Jak vám mohu pomoci?", isFromUser: false)

            // Process any pending uploads
            processPendingUploads()
        }
    }

    private func processPendingUploads() {
        guard !pendingUploads.isEmpty else { return }

        print("🚀 Processing \(pendingUploads.count) pending uploads")

        for upload in pendingUploads {
            print("📤 Auto-uploading: \(upload.fileName)")
            uploadToOCR(data: upload.data, fileName: upload.fileName, mimeType: upload.mimeType)
        }

        // Clear pending uploads
        pendingUploads.removeAll()
        print("✅ All pending uploads processed and cleared")
    }

    private func handleQuickAction(_ action: String) {
        // Handle the upload receipt action by showing file picker
        if action == "Nahrát účtenku" {
            showingActionSheet = true
            return
        }

        addMessage(action, isFromUser: true)

        withAnimation(DesignSystem.Animation.springMedium) {
            showChatMessages = true
        }

        // Simulate AI response
        simulateAIResponse(for: action)
    }

    private func sendMessage() {
        let trimmedMessage = messageText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedMessage.isEmpty else { return }

        addMessage(trimmedMessage, isFromUser: true)
        messageText = ""

        if !showChatMessages {
            withAnimation(DesignSystem.Animation.springMedium) {
                showChatMessages = true
            }
            // Process pending uploads when chat starts
            processPendingUploads()
        }

        // Simulate AI response
        simulateAIResponse(for: trimmedMessage)
    }

    private func addMessage(_ content: String, isFromUser: Bool) {
        let message = ChatMessage(content: content, isFromUser: isFromUser)
        withAnimation(DesignSystem.Animation.springFast) {
            messages.append(message)
        }
    }

    private func addReceiptMessage(_ json: [String: Any], imageData: Data? = nil) {
        let receiptData = ReceiptData(from: json, imageData: imageData)
        let message = ChatMessage(content: "", isFromUser: false, receiptData: receiptData)
        withAnimation(DesignSystem.Animation.springFast) {
            messages.append(message)
        }
    }

    private func addInvestmentPlanMessage() {
        let investmentData = InvestmentPlanData()
        let message = ChatMessage(content: "", isFromUser: false, investmentData: investmentData)
        withAnimation(DesignSystem.Animation.springFast) {
            messages.append(message)
        }
    }

    private func simulateAIResponse(for userMessage: String) {
        isTyping = true

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            isTyping = false

            // Check if user typed "investice" to show investment plan widget
            if userMessage.lowercased().contains("investice") {
                addInvestmentPlanMessage()
                return
            }

            let responses = [
                "Rozumím vašemu dotazu. Jak vám mohu dále pomoci?",
                "Děkuji za informaci. Potřebujete něco dalšího?",
                "To je zajímavé. Můžete mi říct více detailů?",
                "Určitě vám s tím pomohu. Jaké máte další otázky?"
            ]

            let response = responses.randomElement() ?? responses[0]
            addMessage(response, isFromUser: false)
        }
    }

    private func handleVoiceInput() {
        // Handle voice input functionality
        print("Voice input triggered")
    }

    private func resetToInitialState() {
        withAnimation(DesignSystem.Animation.springMedium) {
            showChatMessages = false
            messages.removeAll()
            messageText = ""
            isTyping = false
        }
    }

    /// Dismisses the keyboard using SwiftUI's FocusState approach
    private func dismissKeyboard() {
        // Hide keyboard by removing focus from all text fields
        // This is a SwiftUI-native approach that works without UIKit imports
        #if canImport(UIKit)
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        #endif
    }

    // MARK: - File Processing Methods

    private func requestCameraPermissionAndOpen() {
        print("📷 Requesting camera permission...")
        print("📱 Camera available: \(PermissionHelper.isCameraAvailable())")

        PermissionHelper.requestCameraPermission { granted in
            print("📷 Camera permission result: \(granted)")

            if granted && PermissionHelper.isCameraAvailable() {
                print("✅ Camera permission granted and camera available")
                cameraSourceType = .camera
                showingImagePicker = true
            } else {
                print("❌ Camera access denied or not available")
                print("📷 Permission granted: \(granted)")
                print("📱 Camera available: \(PermissionHelper.isCameraAvailable())")
                addMessage("❌ Camera access denied or not available", isFromUser: false)
            }
        }
    }

    private func processImage(_ image: UIImage) {
        print("📸 Processing image from camera/gallery")
        print("📏 Image size: \(image.size)")

        guard let imageData = image.jpegData(compressionQuality: 0.8) else {
            print("❌ Failed to convert image to JPEG data")
            addMessage("❌ Error processing image", isFromUser: false)
            return
        }

        print("✅ Image converted to JPEG, size: \(imageData.count) bytes")

        if showChatMessages {
            // Chat is active, upload immediately
            uploadToOCR(data: imageData, fileName: "photo.jpg", mimeType: "image/jpeg")
        } else {
            // Chat not started, add to pending uploads
            print("💾 Adding image to pending uploads (chat not started)")
            pendingUploads.append(PendingUpload(data: imageData, fileName: "photo.jpg", mimeType: "image/jpeg"))
            addMessage("📄 Image ready to upload (will send when chat starts)", isFromUser: true)
        }
    }

    private func processDocument(_ url: URL) {
        print("📄 Processing document from file picker")
        print("📁 Document URL: \(url)")
        print("📝 Document path: \(url.path)")

        do {
            let data = try Data(contentsOf: url)
            let fileName = url.lastPathComponent
            let mimeType = getMimeType(for: url)

            print("✅ Document loaded successfully")
            print("📁 File name: \(fileName)")
            print("📄 MIME type: \(mimeType)")
            print("📊 File size: \(data.count) bytes")

            if showChatMessages {
                // Chat is active, upload immediately
                uploadToOCR(data: data, fileName: fileName, mimeType: mimeType)
            } else {
                // Chat not started, add to pending uploads
                print("💾 Adding document to pending uploads (chat not started)")
                pendingUploads.append(PendingUpload(data: data, fileName: fileName, mimeType: mimeType))
                addMessage("📄 \(fileName) ready to upload (will send when chat starts)", isFromUser: true)
            }
        } catch {
            print("❌ Failed to read document: \(error)")
            print("❌ Error details: \(error.localizedDescription)")
            addMessage("Error reading file: \(error.localizedDescription)", isFromUser: false)
        }
    }

    private func processPhotoPickerItem(_ item: PhotosPickerItem) {
        print("🖼️ Processing PhotosPicker item")
        print("📋 Item identifier: \(item.itemIdentifier ?? "nil")")
        print("📄 Supported content types: \(item.supportedContentTypes)")

        item.loadTransferable(type: Data.self) { result in
            DispatchQueue.main.async {
                print("📥 PhotosPicker data loading completed")

                switch result {
                case .success(let data):
                    if let data = data {
                        print("✅ PhotosPicker data loaded successfully")
                        print("📁 File name: photo.jpg")
                        print("📄 MIME type: image/jpeg")
                        print("📊 Data size: \(data.count) bytes")

                        if showChatMessages {
                            // Chat is active, upload immediately
                            uploadToOCR(data: data, fileName: "photo.jpg", mimeType: "image/jpeg")
                        } else {
                            // Chat not started, add to pending uploads
                            print("💾 Adding PhotosPicker image to pending uploads (chat not started)")
                            pendingUploads.append(PendingUpload(data: data, fileName: "photo.jpg", mimeType: "image/jpeg"))
                            addMessage("📄 Image ready to upload (will send when chat starts)", isFromUser: true)
                        }
                    } else {
                        print("❌ PhotosPicker returned nil data")
                        addMessage("❌ No image data received", isFromUser: false)
                    }
                case .failure(let error):
                    print("❌ PhotosPicker failed to load data: \(error)")
                    print("❌ Error details: \(error.localizedDescription)")
                    addMessage("Error loading photo: \(error.localizedDescription)", isFromUser: false)
                }
            }
        }
    }

    private func getMimeType(for url: URL) -> String {
        let pathExtension = url.pathExtension.lowercased()
        switch pathExtension {
        case "pdf":
            return "application/pdf"
        case "jpg", "jpeg":
            return "image/jpeg"
        case "png":
            return "image/png"
        default:
            return "application/octet-stream"
        }
    }

    private func uploadToOCR(data: Data, fileName: String, mimeType: String) {
        guard !isProcessingFile else {
            print("🚫 OCR: Upload blocked - already processing file")
            return
        }

        isProcessingFile = true
        addMessage("📄 Uploading \(fileName)...", isFromUser: true)

        // Store current upload data for receipt creation
        currentUploadData = data

        // Show typing indicator for processing
        isTyping = true

        print("🚀 OCR: Starting upload process")
        print("📁 OCR: File name: \(fileName)")
        print("📄 OCR: MIME type: \(mimeType)")
        print("📊 OCR: File size: \(data.count) bytes")

        // Create multipart form data
        let boundary = UUID().uuidString
        print("🔖 OCR: Boundary: \(boundary)")

        var body = Data()

        // Add file data
        let formDataHeader = "--\(boundary)\r\n"
        let contentDisposition = "Content-Disposition: form-data; name=\"file\"; filename=\"\(fileName)\"\r\n"
        let contentType = "Content-Type: \(mimeType)\r\n\r\n"
        let formDataFooter = "\r\n--\(boundary)--\r\n"

        print("📝 OCR: Form data header: \(formDataHeader)")
        print("📝 OCR: Content disposition: \(contentDisposition)")
        print("📝 OCR: Content type: \(contentType)")

        body.append(formDataHeader.data(using: .utf8)!)
        body.append(contentDisposition.data(using: .utf8)!)
        body.append(contentType.data(using: .utf8)!)
        body.append(data)
        body.append(formDataFooter.data(using: .utf8)!)

        print("📦 OCR: Total body size: \(body.count) bytes")

        // Create request
        let urlString = "https://ocrtool.blackmeadow-6ef652a6.westus2.azurecontainerapps.io/analyze"
        guard let url = URL(string: urlString) else {
            print("❌ OCR: Invalid URL: \(urlString)")
            addMessage("❌ Invalid OCR service URL", isFromUser: false)
            isProcessingFile = false
            isTyping = false
            return
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer Sl4v1e", forHTTPHeaderField: "Authorization")
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")
        request.httpBody = body

        print("🌐 OCR: Request URL: \(urlString)")
        print("🔑 OCR: Authorization: Bearer Sl4v1e")
        print("📋 OCR: Content-Type: multipart/form-data; boundary=\(boundary)")
        print("🚀 OCR: Sending request...")

        // Perform request
        URLSession.shared.dataTask(with: request) { data, response, error in
            DispatchQueue.main.async {
                isProcessingFile = false
                isTyping = false

                print("📥 OCR: Response received")

                // Log HTTP response details
                if let httpResponse = response as? HTTPURLResponse {
                    print("📊 OCR: Status code: \(httpResponse.statusCode)")
                    print("📋 OCR: Response headers: \(httpResponse.allHeaderFields)")
                } else {
                    print("⚠️ OCR: No HTTP response received")
                }

                if let error = error {
                    print("❌ OCR: Network error: \(error)")
                    print("❌ OCR: Error description: \(error.localizedDescription)")
                    addMessage("❌ Error: \(error.localizedDescription)", isFromUser: false)
                    currentUploadData = nil
                    return
                }

                guard let data = data else {
                    print("❌ OCR: No response data received")
                    addMessage("❌ No response data received", isFromUser: false)
                    currentUploadData = nil
                    return
                }

                print("📊 OCR: Response data size: \(data.count) bytes")

                // Log raw response data
                if let rawResponse = String(data: data, encoding: .utf8) {
                    print("📄 OCR: Raw response: \(rawResponse)")
                } else {
                    print("⚠️ OCR: Could not decode response as UTF-8 string")
                    print("📄 OCR: Raw data: \(data)")
                }

                // Parse and format the JSON response
                handleOCRResponse(data)
            }
        }.resume()

        print("✅ OCR: Request initiated successfully")
    }

    private func handleOCRResponse(_ data: Data) {
        print("🔄 OCR: Processing response data...")

        guard let rawResponse = String(data: data, encoding: .utf8) else {
            print("❌ OCR: Could not decode response as UTF-8")
            addMessage("❌ Unable to decode response", isFromUser: false)
            return
        }

        print("📄 OCR: Raw response: \(rawResponse)")

        // First, try to decode the response as a JSON string (since API returns JSON-encoded string)
        var actualJSONString = rawResponse

        // Check if the response is a JSON-encoded string (starts and ends with quotes)
        if rawResponse.hasPrefix("\"") && rawResponse.hasSuffix("\"") {
            print("🔧 OCR: Response appears to be JSON-encoded string, decoding...")
            do {
                // Parse the outer JSON string to get the inner content
                if let decodedString = try JSONSerialization.jsonObject(with: data) as? String {
                    actualJSONString = decodedString
                    print("📄 OCR: Decoded JSON string: \(actualJSONString)")
                }
            } catch {
                print("⚠️ OCR: Failed to decode JSON string, using raw response: \(error)")
            }
        }

        // Extract JSON from markdown code blocks if present
        let jsonString = extractJSONFromMarkdown(actualJSONString)
        print("📄 OCR: Extracted JSON: \(jsonString)")

        guard let jsonData = jsonString.data(using: .utf8) else {
            print("❌ OCR: Could not convert extracted JSON to data")
            addMessage("📄 OCR Result:\n\(rawResponse)", isFromUser: false)
            return
        }

        do {
            if let json = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                print("✅ OCR: Successfully parsed JSON response")
                print("📋 OCR: JSON keys: \(Array(json.keys))")

                // Log each key-value pair for debugging
                for (key, value) in json {
                    print("📝 OCR: \(key): \(value)")
                }

                // Create receipt card instead of formatted text
                addReceiptMessage(json, imageData: currentUploadData)
                print("📄 OCR: Receipt card created successfully")

                // Clear the stored upload data
                currentUploadData = nil
            } else {
                print("⚠️ OCR: Response is not a JSON object, treating as raw text")
                addMessage("📄 OCR Result:\n\(rawResponse)", isFromUser: false)
            }
        } catch {
            print("❌ OCR: JSON parsing failed: \(error)")
            print("❌ OCR: Error details: \(error.localizedDescription)")
            addMessage("📄 OCR Result:\n\(rawResponse)", isFromUser: false)
            currentUploadData = nil
        }

        print("✅ OCR: Response handling completed")
    }

    private func extractJSONFromMarkdown(_ text: String) -> String {
        print("🔧 OCR: Extracting JSON from markdown wrapper")
        print("📄 OCR: Original text: \(text)")

        // First try to extract JSON from markdown code blocks
        if text.contains("```json") || text.contains("```") {
            let patterns = [
                "```json\\s*\\n",  // Opening ```json
                "\\n```",          // Closing ```
                "```"              // Simple ```
            ]

            var cleanedText = text
            for pattern in patterns {
                cleanedText = cleanedText.replacingOccurrences(
                    of: pattern,
                    with: "",
                    options: .regularExpression
                )
            }

            let result = cleanedText.trimmingCharacters(in: .whitespacesAndNewlines)
            print("📄 OCR: Cleaned from markdown: \(result)")
            return result
        }

        // If no markdown wrapper, try to find JSON object directly
        if let startIndex = text.firstIndex(of: "{"),
           let endIndex = text.lastIndex(of: "}") {
            let jsonSubstring = text[startIndex...endIndex]
            let result = String(jsonSubstring)
            print("📄 OCR: Extracted JSON object: \(result)")
            return result
        }

        // Return original text if no JSON structure found
        print("📄 OCR: No JSON structure found, returning original")
        return text.trimmingCharacters(in: .whitespacesAndNewlines)
    }

    private func formatOCRResponse(_ json: [String: Any]) -> String {
        var result = "📄 **OCR Analysis Results**\n\n"

        // Extract common fields that might be in the response
        if let text = json["text"] as? String {
            result += "**Extracted Text:**\n\(text)\n\n"
        }

        if let items = json["items"] as? [[String: Any]] {
            result += "**Items Found:**\n"
            for (index, item) in items.enumerated() {
                result += "\(index + 1). "
                if let description = item["description"] as? String {
                    result += description
                }
                if let amount = item["amount"] as? Double {
                    result += " - \(String(format: "%.2f", amount))"
                }
                if let currency = item["currency"] as? String {
                    result += " \(currency)"
                }
                result += "\n"
            }
            result += "\n"
        }

        if let total = json["total"] as? Double {
            result += "**Total Amount:** \(String(format: "%.2f", total))"
            if let currency = json["currency"] as? String {
                result += " \(currency)"
            }
            result += "\n\n"
        }

        if let vendor = json["vendor"] as? String {
            result += "**Vendor:** \(vendor)\n"
        }

        if let date = json["date"] as? String {
            result += "**Date:** \(date)\n"
        }

        // If no specific fields found, show formatted JSON
        if result == "📄 **OCR Analysis Results**\n\n" {
            result += "**Raw Data:**\n"
            if let jsonData = try? JSONSerialization.data(withJSONObject: json, options: .prettyPrinted),
               let jsonString = String(data: jsonData, encoding: .utf8) {
                result += "```json\n\(jsonString)\n```"
            }
        }

        return result
    }
}

// MARK: - Typing Indicator

struct TypingIndicator: View {
    @State private var animationOffset: CGFloat = 0

    var body: some View {
        HStack {
            HStack(spacing: DesignSystem.Spacing.xs) {
                ForEach(0..<3) { index in
                    Circle()
                        .fill(DesignSystem.Colors.textTertiary)
                        .frame(width: 8, height: 8)
                        .offset(y: animationOffset)
                        .animation(
                            DesignSystem.Animation.typingDot.delay(Double(index) * 0.2),
                            value: animationOffset
                        )
                }
            }
            .padding(.horizontal, DesignSystem.Spacing.lg)
            .padding(.vertical, DesignSystem.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.messageBubble)
                    .fill(DesignSystem.Colors.surfacePrimary)
            )

            Spacer()
        }
        .onAppear {
            animationOffset = -4
        }
    }
}

// MARK: - Chat Message Model

struct ChatMessage: Identifiable, Equatable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
    let receiptData: ReceiptData?
    let investmentData: InvestmentPlanData?

    init(content: String, isFromUser: Bool, receiptData: ReceiptData? = nil, investmentData: InvestmentPlanData? = nil) {
        self.content = content
        self.isFromUser = isFromUser
        self.timestamp = Date()
        self.receiptData = receiptData
        self.investmentData = investmentData
    }

    static func == (lhs: ChatMessage, rhs: ChatMessage) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Scroll Offset Preference Key

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0

    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

// MARK: - Preview

#Preview {
    ChatView()
}

// MARK: - Investment Plan Data Model

struct InvestmentPlanData {
    let targetAmount: String = "4 mil"
    let currency: String = "CZK"
    let investmentPeriodYears: String = "16"
    let investmentPeriodMonths: String = "1"
    let regularDeposit: String = "4 500 Kč"
    let oneTimeDeposit: String = "0 Kč"
    let investorType: String = "Posera"
}

// MARK: - Investment Plan Widget

struct InvestmentPlanWidget: View {
    let investmentData: InvestmentPlanData

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header section with target amount and investment period
            VStack(alignment: .leading, spacing: 16) {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(alignment: .firstTextBaseline, spacing: 8) {
                        // Target amount section
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Cílová částka")
                                .font(.custom("SF Pro", size: 13))
                                .lineSpacing(18)
                                .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                            HStack(alignment: .firstTextBaseline, spacing: 2) {
                                Text(investmentData.targetAmount)
                                    .font(.custom("SF Pro", size: 28).weight(.bold))
                                    .tracking(0.38)
                                    .lineSpacing(34)
                                    .foregroundColor(.white)
                                Text(investmentData.currency)
                                    .font(.custom("SF Pro", size: 15))
                                    .lineSpacing(20)
                                    .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                                ZStack {
                                    // Edit icon placeholder
                                }
                                .frame(width: 20, height: 20)
                            }
                        }

                        // Investment period section
                        VStack(alignment: .leading, spacing: 2) {
                            Text("Doba investování")
                                .font(.custom("SF Pro", size: 13))
                                .lineSpacing(18)
                                .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                            HStack(spacing: 4) {
                                HStack(alignment: .firstTextBaseline, spacing: 2) {
                                    Text(investmentData.investmentPeriodYears)
                                        .font(.custom("SF Pro", size: 28).weight(.bold))
                                        .tracking(0.38)
                                        .lineSpacing(34)
                                        .foregroundColor(.white)
                                    Text("let")
                                        .font(.custom("SF Pro", size: 15))
                                        .lineSpacing(20)
                                        .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                                }
                                HStack(alignment: .firstTextBaseline, spacing: 2) {
                                    Text(investmentData.investmentPeriodMonths)
                                        .font(.custom("SF Pro", size: 28).weight(.bold))
                                        .tracking(0.38)
                                        .lineSpacing(34)
                                        .foregroundColor(.white)
                                    Text("měsíc")
                                        .font(.custom("SF Pro", size: 15))
                                        .lineSpacing(20)
                                        .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                                }
                                ZStack {
                                    // Edit icon placeholder
                                }
                                .frame(width: 20, height: 20)
                            }
                        }
                    }
                }
            }
            .padding(EdgeInsets(top: 0, leading: 0, bottom: 16, trailing: 0))
            .frame(height: 58)
            .cornerRadius(10)

            // Chart section
            VStack(alignment: .leading, spacing: 8) {
                // Chart placeholder with grid lines
                ZStack {
                    // Grid lines
                    Group {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 175, height: 0)
                            .overlay(
                                Rectangle()
                                    .stroke(Color(red: 0.26, green: 0.27, blue: 0.30), lineWidth: 0.25)
                            )
                            .offset(x: 182.79, y: -86.50)
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 176, height: 0)
                            .overlay(
                                Rectangle()
                                    .stroke(Color(red: 0.26, green: 0.27, blue: 0.30), lineWidth: 0.25)
                            )
                            .offset(x: -74, y: -87.50)
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 324, height: 0)
                            .overlay(
                                Rectangle()
                                    .stroke(Color(red: 0.26, green: 0.27, blue: 0.30), lineWidth: 0.25)
                            )
                            .offset(x: 324, y: 88.50)
                    }
                }
                .frame(width: 324, height: 177)

                // Chart labels
                ZStack {
                    Text("dnes")
                        .font(.custom("SF Pro", size: 12))
                        .lineSpacing(16)
                        .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                        .offset(x: -85, y: 0)
                    Text("16 let")
                        .font(.custom("SF Pro", size: 12))
                        .lineSpacing(16)
                        .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                        .offset(x: 97.50, y: 0)
                    Text("10 let")
                        .font(.custom("SF Pro", size: 12))
                        .lineSpacing(16)
                        .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                        .offset(x: 22.50, y: 0)
                    Text("20 let")
                        .font(.custom("SF Pro", size: 12))
                        .lineSpacing(16)
                        .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                        .offset(x: 149.50, y: 0)
                }
                .frame(height: 16)

                // Legend
                HStack(alignment: .top, spacing: 16) {
                    HStack(spacing: 8) {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 10, height: 10)
                            .background(Color(red: 0.51, green: 0.71, blue: 0.81))
                            .cornerRadius(4)
                        Text("Optimistický")
                            .font(.custom("SF Pro", size: 11))
                            .lineSpacing(14)
                            .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                    }
                    HStack(spacing: 8) {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 10, height: 10)
                            .background(Color(red: 0.81, green: 0.88, blue: 0.91))
                            .cornerRadius(4)
                        Text("Reálný")
                            .font(.custom("SF Pro", size: 11))
                            .lineSpacing(14)
                            .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                    }
                    HStack(spacing: 8) {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 10, height: 10)
                            .background(Color(red: 0.57, green: 0.58, blue: 0.63))
                            .cornerRadius(4)
                        Text("Pesimistický")
                            .font(.custom("SF Pro", size: 11))
                            .lineSpacing(14)
                            .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                    }
                    HStack(spacing: 8) {
                        Rectangle()
                            .foregroundColor(.clear)
                            .frame(width: 10, height: 10)
                            .cornerRadius(4)
                            .overlay(
                                RoundedRectangle(cornerRadius: 4)
                                    .inset(by: 0.50)
                                    .stroke(Color(red: 0.57, green: 0.58, blue: 0.63), lineWidth: 0.50)
                            )
                        Text("Vklad")
                            .font(.custom("SF Pro", size: 11))
                            .lineSpacing(14)
                            .foregroundColor(Color(red: 0.75, green: 0.75, blue: 0.78))
                    }
                }
                .frame(width: 326, height: 14)
            }
            .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))

            // Investment settings section
            VStack(alignment: .leading, spacing: 8) {
                // Regular deposit
                VStack(alignment: .leading, spacing: 0) {
                    HStack(alignment: .firstTextBaseline, spacing: 8) {
                        Text("Pravidelný vklad")
                            .font(.custom("SF Pro", size: 13))
                            .lineSpacing(18)
                            .foregroundColor(.white)
                        VStack(alignment: .trailing, spacing: 4) {
                            HStack(spacing: 2) {
                                HStack(spacing: 8) {
                                    Text(investmentData.regularDeposit)
                                        .font(.custom("SF Pro", size: 15))
                                        .lineSpacing(20)
                                        .foregroundColor(Color(red: 0.20, green: 0.57, blue: 0.76))
                                }
                                .padding(EdgeInsets(top: 0, leading: 10, bottom: 0, trailing: 0))
                                ZStack {
                                    // Edit icon placeholder
                                }
                                .frame(width: 20, height: 20)
                            }
                        }
                    }
                    .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))
                    .cornerRadius(10)

                    // Slider for regular deposit
                    VStack(alignment: .leading, spacing: 10) {
                        ZStack {
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 340, height: 4)
                                .background(Color(red: 0.26, green: 0.27, blue: 0.30))
                                .cornerRadius(16)
                                .offset(x: 0, y: 0)
                            HStack(spacing: 100) {
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(width: 0.01, height: 4)
                                    .cornerRadius(2)
                            }
                            .frame(height: 4)
                            .background(Color(red: 0.20, green: 0.57, blue: 0.76))
                            .cornerRadius(2)
                            .offset(x: -106, y: 0)
                        }
                        .frame(height: 28)
                    }
                    .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))
                }

                // Divider
                ZStack {
                    Rectangle()
                        .foregroundColor(.clear)
                        .frame(width: 326, height: 0)
                        .overlay(
                            Rectangle()
                                .stroke(Color(red: 0.26, green: 0.27, blue: 0.30), lineWidth: 0.50)
                        )
                        .offset(x: 0, y: 0)
                }
                .frame(width: 326, height: 0)

                // One-time deposit
                VStack(alignment: .leading, spacing: 0) {
                    HStack(alignment: .firstTextBaseline, spacing: 8) {
                        Text("Jednorázový vklad")
                            .font(.custom("SF Pro", size: 13))
                            .lineSpacing(18)
                            .foregroundColor(.white)
                        VStack(alignment: .trailing, spacing: 4) {
                            HStack(spacing: 2) {
                                HStack(spacing: 8) {
                                    Text(investmentData.oneTimeDeposit)
                                        .font(.custom("SF Pro", size: 15))
                                        .lineSpacing(20)
                                        .foregroundColor(Color(red: 0.20, green: 0.57, blue: 0.76))
                                }
                                .padding(EdgeInsets(top: 0, leading: 10, bottom: 0, trailing: 0))
                                ZStack {
                                    // Edit icon placeholder
                                }
                                .frame(width: 20, height: 20)
                            }
                        }
                    }
                    .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))
                    .cornerRadius(10)

                    // Slider for one-time deposit (empty)
                    VStack(alignment: .leading, spacing: 10) {
                        ZStack {
                            Rectangle()
                                .foregroundColor(.clear)
                                .frame(width: 340, height: 4)
                                .background(Color(red: 0.26, green: 0.27, blue: 0.30))
                                .cornerRadius(16)
                                .offset(x: 0, y: 0)
                            HStack(spacing: 0) {
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(width: 0.01, height: 4)
                                    .cornerRadius(2)
                            }
                            .frame(width: 128.01, height: 4)
                            .background(Color(red: 0.15, green: 0.16, blue: 0.17).opacity(0))
                            .cornerRadius(2)
                            .offset(x: -106, y: 0)
                        }
                        .frame(height: 28)
                    }
                    .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))
                }

                // Divider
                ZStack {
                    Rectangle()
                        .foregroundColor(.clear)
                        .frame(width: 326, height: 0)
                        .overlay(
                            Rectangle()
                                .stroke(Color(red: 0.26, green: 0.27, blue: 0.30), lineWidth: 0.50)
                        )
                        .offset(x: 0, y: 0)
                }
                .frame(width: 326, height: 0)

                // Investor type
                VStack(alignment: .leading, spacing: 0) {
                    HStack(alignment: .firstTextBaseline, spacing: 8) {
                        HStack(spacing: 4) {
                            Text("Jaký jsem investor?")
                                .font(.custom("SF Pro", size: 13))
                                .lineSpacing(18)
                                .foregroundColor(.white)
                            HStack(alignment: .top, spacing: 16) {
                                ZStack {
                                    // Info icon placeholder
                                }
                                .frame(width: 20, height: 20)
                            }
                        }
                        VStack(alignment: .trailing, spacing: 4) {
                            HStack(spacing: 2) {
                                HStack(spacing: 8) {
                                    Text(investmentData.investorType)
                                        .font(.custom("SF Pro", size: 15))
                                        .lineSpacing(20)
                                        .foregroundColor(Color(red: 0.20, green: 0.57, blue: 0.76))
                                }
                                .padding(EdgeInsets(top: 0, leading: 10, bottom: 0, trailing: 0))
                            }
                        }
                    }
                    .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))
                    .cornerRadius(10)

                    // Risk level indicator
                    VStack(alignment: .leading, spacing: 10) {
                        ZStack {
                            HStack(alignment: .top, spacing: 8) {
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.76, green: 0.23, blue: 0.26))
                                    .cornerRadius(2)
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.76, green: 0.23, blue: 0.26))
                                    .cornerRadius(2)
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.81, green: 0.51, blue: 0.06))
                                    .cornerRadius(2)
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.81, green: 0.51, blue: 0.06))
                                    .cornerRadius(2)
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.81, green: 0.51, blue: 0.06))
                                    .cornerRadius(2)
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.35, green: 0.73, blue: 0.42))
                                    .cornerRadius(2)
                                Rectangle()
                                    .foregroundColor(.clear)
                                    .frame(height: 4)
                                    .background(Color(red: 0.35, green: 0.73, blue: 0.42))
                                    .cornerRadius(2)
                            }
                            .padding(EdgeInsets(top: 12, leading: 0, bottom: 12, trailing: 0))
                            .frame(width: 338)
                            .offset(x: -1, y: 0)
                        }
                        .frame(height: 28)
                    }
                    .padding(EdgeInsets(top: 8, leading: 0, bottom: 8, trailing: 0))
                }
            }
        }
        .padding(16)
        .background(Color(red: 0.15, green: 0.16, blue: 0.17))
        .cornerRadius(10)
        .shadow(
            color: Color(red: 0, green: 0, blue: 0, opacity: 0.04), radius: 3
        )
    }
}

// MARK: - Helper Views
// Note: Using PhotosPicker for file selection to avoid UIKit dependencies