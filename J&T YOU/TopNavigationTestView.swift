//
//  TopNavigationTestView.swift
//  J&T YOU
//
//  Created by <PERSON><PERSON> on 29.05.2025.
//

import SwiftUI

/// Test view to showcase the new TopNavigation component with App Library style blur
struct TopNavigationTestView: View {
    @State private var scrollOffset: CGFloat = 0
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                DesignSystemComponents.AppBackground()
                
                // Main content with scroll
                ScrollView {
                    GeometryReader { scrollGeometry in
                        Color.clear.preference(
                            key: ScrollOffsetPreferenceKey.self,
                            value: scrollGeometry.frame(in: .named("scroll")).minY
                        )
                    }
                    .frame(height: 0)
                    
                    LazyVStack(spacing: DesignSystem.Spacing.lg) {
                        // Header content
                        VStack(spacing: DesignSystem.Spacing.xl) {
                            Text("TopNavigation Demo")
                                .font(DesignSystem.Typography.largeTitleFont)
                                .foregroundColor(DesignSystem.Colors.textPrimary)
                                .padding(.top, DesignSystem.Spacing.xxxl)
                            
                            Text("Scroll down to see the App Library style blur effect")
                                .font(DesignSystem.Typography.bodyFont)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.horizontal, DesignSystem.Spacing.lg)
                        
                        // Sample content cards
                        ForEach(0..<20, id: \.self) { index in
                            DesignSystemComponents.GlassmorphicContainer {
                                VStack(alignment: .leading, spacing: DesignSystem.Spacing.md) {
                                    Text("Content Card \(index + 1)")
                                        .font(DesignSystem.Typography.headlineFont)
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    Text("This is sample content to demonstrate the scroll behavior and blur effect. The navigation bar should become more prominent as you scroll.")
                                        .font(DesignSystem.Typography.bodyFont)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                        .multilineTextAlignment(.leading)
                                    
                                    HStack {
                                        Spacer()
                                        Text("Scroll Offset: \(Int(scrollOffset))")
                                            .font(DesignSystem.Typography.caption1Font)
                                            .foregroundColor(DesignSystem.Colors.textTertiary)
                                    }
                                }
                                .padding(DesignSystem.Spacing.lg)
                            }
                            .padding(.horizontal, DesignSystem.Spacing.lg)
                        }
                    }
                    .safeAreaInset(edge: .top) {
                        Color.clear.frame(height: 100) // Space for navigation
                    }
                    .safeAreaInset(edge: .bottom) {
                        Color.clear.frame(height: DesignSystem.Spacing.xl)
                    }
                }
                .coordinateSpace(name: "scroll")
                .onPreferenceChange(ScrollOffsetPreferenceKey.self) { value in
                    scrollOffset = -value
                }
                
                // TopNavigation overlay
                VStack(spacing: 0) {
                    DesignSystemComponents.TopNavigation(
                        title: "Demo",
                        isVisible: true,
                        scrollOffset: scrollOffset,
                        leading: {
                            DesignSystemComponents.CircularIconButton(icon: "arrow.left") {
                                // Back action
                            }
                        },
                        trailing: {
                            HStack(spacing: DesignSystem.Spacing.sm) {
                                DesignSystemComponents.CircularIconButton(icon: "heart") {
                                    // Favorite action
                                }
                                DesignSystemComponents.CircularIconButton(icon: "square.and.arrow.up") {
                                    // Share action
                                }
                            }
                        }
                    )
                    
                    Spacer()
                }
            }
        }
        .ignoresSafeArea(.container, edges: .horizontal)
    }
}

// MARK: - Preview

#Preview {
    TopNavigationTestView()
}
